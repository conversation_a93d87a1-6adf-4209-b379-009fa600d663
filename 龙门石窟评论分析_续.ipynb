{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 龙门石窟评论分析 - 续篇\n", "## 5. 文本聚类与LDA主题建模"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# K-means聚类\n", "n_clusters = 3\n", "kmeans = KMeans(n_clusters=n_clusters, random_state=42)\n", "cluster_labels = kmeans.fit_predict(tfidf_matrix)\n", "\n", "print(f\"聚类完成，共 {n_clusters} 个簇\")\n", "print(f\"各簇样本数量: {pd.Series(cluster_labels).value_counts().sort_index()}\")\n", "\n", "# PCA降维可视化\n", "pca = PCA(n_components=2, random_state=42)\n", "tfidf_2d = pca.fit_transform(tfidf_matrix.toarray())\n", "\n", "# 聚类结果可视化\n", "plt.figure(figsize=(10, 8))\n", "colors = ['red', 'blue', 'green', 'purple', 'orange']\n", "\n", "for i in range(n_clusters):\n", "    mask = cluster_labels == i\n", "    plt.scatter(tfidf_2d[mask, 0], tfidf_2d[mask, 1], \n", "                c=colors[i], label=f'簇 {i}', alpha=0.6)\n", "\n", "plt.xlabel('第一主成分')\n", "plt.ylabel('第二主成分')\n", "plt.title('K-means聚类结果 (PCA降维)')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析每个簇的特征词\n", "feature_names = np.array(tfidf.get_feature_names_out())\n", "cluster_centers = kmeans.cluster_centers_\n", "\n", "print(\"各簇特征词分析:\")\n", "for i in range(n_clusters):\n", "    # 获取该簇中心的特征权重\n", "    center = cluster_centers[i]\n", "    # 找出权重最高的词\n", "    top_indices = center.argsort()[-10:][::-1]\n", "    top_words = feature_names[top_indices]\n", "    top_weights = center[top_indices]\n", "    \n", "    print(f\"\\n簇 {i} 的特征词:\")\n", "    for word, weight in zip(top_words, top_weights):\n", "        print(f\"  {word}: {weight:.4f}\")\n", "    \n", "    # 显示该簇的一些样本评论\n", "    cluster_comments = [cleaned_comments[j] for j in range(len(cleaned_comments)) if cluster_labels[j] == i]\n", "    print(f\"\\n簇 {i} 评论示例:\")\n", "    for j, comment in enumerate(cluster_comments[:3]):\n", "        print(f\"  {j+1}. {comment[:100]}...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# LDA主题建模\n", "from gensim import corpora, models\n", "import pyLDAvis.gensim_models as gensimvis\n", "import pyLDAvis\n", "\n", "# 准备LDA数据\n", "texts = segmented_comments\n", "dictionary = corpora.Dictionary(texts)\n", "corpus = [dictionary.doc2bow(text) for text in texts]\n", "\n", "print(f\"词典大小: {len(dictionary)}\")\n", "print(f\"语料库大小: {len(corpus)}\")\n", "\n", "# 训练LDA模型\n", "num_topics = 4\n", "lda_model = models.LdaModel(\n", "    corpus=corpus,\n", "    id2word=dictionary,\n", "    num_topics=num_topics,\n", "    random_state=42,\n", "    passes=10,\n", "    alpha='auto',\n", "    per_word_topics=True\n", ")\n", "\n", "print(f\"\\nLDA模型训练完成，共 {num_topics} 个主题\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 显示主题及其关键词\n", "print(\"LDA主题分析结果:\")\n", "for idx, topic in lda_model.print_topics(-1):\n", "    print(f\"\\n主题 {idx}:\")\n", "    print(topic)\n", "\n", "# 提取每个主题的Top10关键词\n", "topics_keywords = {}\n", "for topic_id in range(num_topics):\n", "    topic_words = lda_model.show_topic(topic_id, topn=10)\n", "    topics_keywords[f'主题{topic_id}'] = [(word, prob) for word, prob in topic_words]\n", "    \n", "    print(f\"\\n主题 {topic_id} Top10关键词:\")\n", "    for word, prob in topic_words:\n", "        print(f\"  {word}: {prob:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pyLDAvis可视化\n", "try:\n", "    # 生成交互式主题分布图\n", "    vis = gensimvis.prepare(lda_model, corpus, dictionary)\n", "    \n", "    # 保存为HTML文件\n", "    pyLDAvis.save_html(vis, '龙门石窟LDA主题分析.html')\n", "    print(\"LDA可视化已保存为 '龙门石窟LDA主题分析.html'\")\n", "    \n", "    # 在<PERSON><PERSON><PERSON>中显示\n", "    pyLDAvis.display(vis)\n", "    \n", "except Exception as e:\n", "    print(f\"LDA可视化出现错误: {e}\")\n", "    print(\"请检查pyLDAvis安装是否正确\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 观点挖掘与社交网络分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 观点提取：基于依存句法分析\n", "import jieba.posseg as pseg\n", "\n", "def extract_opinions(text):\n", "    \"\"\"提取观点：主语+评价词组合\"\"\"\n", "    opinions = []\n", "    words = pseg.cut(text)\n", "    \n", "    word_list = list(words)\n", "    \n", "    # 简化的观点提取：寻找名词+形容词的组合\n", "    for i in range(len(word_list) - 1):\n", "        current_word, current_flag = word_list[i]\n", "        next_word, next_flag = word_list[i + 1]\n", "        \n", "        # 名词 + 形容词\n", "        if current_flag in ['n', 'nr', 'ns', 'nt', 'nz'] and next_flag in ['a', 'ad', 'an']:\n", "            if len(current_word) >= 2 and len(next_word) >= 2:\n", "                opinions.append(f\"{current_word} → {next_word}\")\n", "        \n", "        # 形容词 + 名词\n", "        elif current_flag in ['a', 'ad', 'an'] and next_flag in ['n', 'nr', 'ns', 'nt', 'nz']:\n", "            if len(current_word) >= 2 and len(next_word) >= 2:\n", "                opinions.append(f\"{next_word} → {current_word}\")\n", "    \n", "    return opinions\n", "\n", "# 提取所有评论的观点\n", "all_opinions = []\n", "for comment in cleaned_comments:\n", "    opinions = extract_opinions(comment)\n", "    all_opinions.extend(opinions)\n", "\n", "# 统计观点频次\n", "opinion_freq = Counter(all_opinions)\n", "top_opinions = opinion_freq.most_common(20)\n", "\n", "print(f\"提取观点总数: {len(all_opinions)}\")\n", "print(f\"不重复观点数: {len(opinion_freq)}\")\n", "print(\"\\nTop20观点:\")\n", "for opinion, count in top_opinions:\n", "    print(f\"{opinion}: {count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 社交网络图构建\n", "import networkx as nx\n", "import hashlib\n", "\n", "# 生成用户ID（基于评论时间哈希）\n", "def generate_user_id(user_name, index):\n", "    \"\"\"生成用户ID\"\"\"\n", "    if pd.isna(user_name) or user_name == 'nan':\n", "        return f\"用户_{index}\"\n", "    return str(user_name)\n", "\n", "user_ids = [generate_user_id(user, i) for i, user in enumerate(users)]\n", "\n", "# 构建用户-关键词矩阵\n", "user_keywords = defaultdict(set)\n", "for i, words in enumerate(segmented_comments):\n", "    user_id = user_ids[i]\n", "    # 只保留高频词\n", "    high_freq_words = [word for word in words if word_freq.get(word, 0) >= 3]\n", "    user_keywords[user_id].update(high_freq_words)\n", "\n", "print(f\"用户数量: {len(user_keywords)}\")\n", "print(f\"平均每用户关键词数: {np.mean([len(keywords) for keywords in user_keywords.values()]):.1f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 构建社交网络图\n", "G = nx.Graph()\n", "\n", "# 添加节点（用户）\n", "for user_id in user_keywords.keys():\n", "    G.add_node(user_id)\n", "\n", "# 添加边（基于共同关键词）\n", "threshold = 3  # 共同关键词阈值\n", "edges_added = 0\n", "\n", "user_list = list(user_keywords.keys())\n", "for i in range(len(user_list)):\n", "    for j in range(i + 1, len(user_list)):\n", "        user1, user2 = user_list[i], user_list[j]\n", "        \n", "        # 计算共同关键词数量\n", "        common_keywords = user_keywords[user1] & user_keywords[user2]\n", "        \n", "        if len(common_keywords) >= threshold:\n", "            G.add_edge(user1, user2, weight=len(common_keywords))\n", "            edges_added += 1\n", "\n", "print(f\"网络节点数: {G.number_of_nodes()}\")\n", "print(f\"网络边数: {G.number_of_edges()}\")\n", "print(f\"网络密度: {nx.density(G):.4f}\")\n", "\n", "# 计算网络指标\n", "if G.number_of_edges() > 0:\n", "    # 度中心性\n", "    degree_centrality = nx.degree_centrality(G)\n", "    # 介数中心性\n", "    betweenness_centrality = nx.betweenness_centrality(G)\n", "    \n", "    print(\"\\n度中心性最高的用户:\")\n", "    top_degree = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)[:5]\n", "    for user, centrality in top_degree:\n", "        print(f\"{user}: {centrality:.4f}\")\n", "else:\n", "    print(\"网络中没有边，无法计算中心性指标\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 网络可视化\n", "if G.number_of_edges() > 0:\n", "    plt.figure(figsize=(12, 10))\n", "    \n", "    # 使用spring布局\n", "    pos = nx.spring_layout(G, k=1, iterations=50)\n", "    \n", "    # 绘制网络\n", "    nx.draw_networkx_nodes(G, pos, node_color='lightblue', \n", "                          node_size=100, alpha=0.7)\n", "    nx.draw_networkx_edges(G, pos, alpha=0.5, width=0.5)\n", "    \n", "    # 只显示度中心性较高的节点标签\n", "    if 'degree_centrality' in locals():\n", "        high_centrality_nodes = {node: centrality for node, centrality \n", "                               in degree_centrality.items() if centrality > 0.1}\n", "        nx.draw_networkx_labels(G, pos, labels=high_centrality_nodes, \n", "                               font_size=8)\n", "    \n", "    plt.title('龙门石窟评论用户社交网络图')\n", "    plt.axis('off')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 保存网络图\n", "    plt.savefig('龙门石窟社交网络图.png', dpi=300, bbox_inches='tight')\n", "else:\n", "    print(\"网络中没有边，无法绘制网络图\")\n", "    print(\"建议降低共同关键词阈值或增加数据量\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}