#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
携程洛阳龙门石窟景点评论多模态文本分析
完整分析脚本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import jieba
import jieba.posseg as pseg
from snownlp import SnowNLP
from wordcloud import WordCloud
import re
from collections import Counter, defaultdict
import warnings
warnings.filterwarnings('ignore')

# 机器学习
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split
from sklearn.tree import DecisionTreeClassifier
from sklearn.naive_bayes import MultinomialNB
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix, classification_report
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA

# 主题建模和网络分析
from gensim import corpora, models
import networkx as nx

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_and_preprocess_data():
    """数据加载与预处理"""
    print("1. 数据加载与预处理...")
    
    # 读取数据
    df = pd.read_excel('携程洛阳龙门石窟景点评论.xlsx')
    print(f"数据形状: {df.shape}")
    
    # 提取关键字段
    comments = df['字段3'].astype(str).tolist()
    ratings = df['字段2'].astype(str).tolist()
    users = df['字段1'].astype(str).tolist()
    
    # 自定义词典
    custom_words = ['龙门石窟', '石窟', '佛像', '卢舍那大佛', '奉先寺', '万佛洞', '古阳洞', 
                   '宾阳洞', '莲花洞', '药方洞', '伊河', '洛阳', '北魏', '唐代', '雕刻', 
                   '文化遗产', '世界遗产', '佛教', '艺术', '历史', '文化', '古迹', 
                   '景区', '门票', '导游', '讲解', '游览', '参观', '拍照', '摄影']
    for word in custom_words:
        jieba.add_word(word)
    
    # 停用词
    stop_words = set(['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', 
                     '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', 
                     '看', '好', '自己', '这', '那', '里', '就是', '还是', '比较', '非常', 
                     '特别', '真的', '觉得', '感觉', '可以', '应该', '已经', '如果', '因为', 
                     '所以', '但是', '不过', '还有', '只是', '或者', '虽然', '然后', '而且'])
    
    # 文本清洗和分词
    def clean_and_segment(text):
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', '', text)
        text = re.sub(r'\s+', ' ', text).strip()
        words = jieba.lcut(text, cut_all=False)
        return [word for word in words if word not in stop_words and len(word) >= 2]
    
    cleaned_comments = [re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', '', comment) for comment in comments]
    segmented_comments = [clean_and_segment(comment) for comment in cleaned_comments]
    
    print("数据预处理完成")
    return comments, cleaned_comments, segmented_comments, users

def sentiment_analysis(cleaned_comments):
    """情感分析"""
    print("2. 情感分析...")
    
    sentiment_scores = [SnowNLP(comment).sentiments for comment in cleaned_comments]
    sentiment_labels = ['积极' if s > 0.6 else '消极' if s < 0.4 else '中性' for s in sentiment_scores]
    
    # 情感分布可视化
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    ax1.hist(sentiment_scores, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_xlabel('情感分数')
    ax1.set_ylabel('频次')
    ax1.set_title('情感分数分布直方图')
    ax1.axvline(np.mean(sentiment_scores), color='red', linestyle='--', 
                label=f'平均值: {np.mean(sentiment_scores):.3f}')
    ax1.legend()
    
    sentiment_counts = pd.Series(sentiment_labels).value_counts()
    ax2.pie(sentiment_counts.values, labels=sentiment_counts.index, autopct='%1.1f%%', 
            colors=['#ff9999', '#66b3ff', '#99ff99'], startangle=90)
    ax2.set_title('情感倾向分布饼图')
    
    plt.tight_layout()
    plt.savefig('情感分析结果.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"平均情感分数: {np.mean(sentiment_scores):.3f}")
    print(f"情感分布: {sentiment_counts}")
    
    return sentiment_scores, sentiment_labels

def word_frequency_analysis(segmented_comments):
    """词频分析与词云"""
    print("3. 词频分析与词云...")
    
    # TF-IDF分析
    text_for_tfidf = [' '.join(words) for words in segmented_comments]
    tfidf = TfidfVectorizer(max_features=2000, ngram_range=(1, 2))
    tfidf_matrix = tfidf.fit_transform(text_for_tfidf)
    
    feature_names = tfidf.get_feature_names_out()
    tfidf_scores = tfidf_matrix.sum(axis=0).A1
    word_freq_df = pd.DataFrame({
        '词语': feature_names, 
        'TF-IDF分数': tfidf_scores
    }).sort_values('TF-IDF分数', ascending=False)
    
    print("TF-IDF权重最高的20个词:")
    print(word_freq_df.head(20))
    
    # 词频统计
    all_words = [word for words in segmented_comments for word in words]
    word_freq = Counter(all_words)
    
    # 生成词云
    wordcloud = WordCloud(
        width=800, height=600, background_color='white', 
        max_words=100, colormap='viridis'
    ).generate_from_frequencies(word_freq)
    
    plt.figure(figsize=(12, 8))
    plt.imshow(wordcloud, interpolation='bilinear')
    plt.axis('off')
    plt.title('龙门石窟评论词云图', fontsize=16, pad=20)
    plt.savefig('龙门石窟词云图.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("词云图已保存")
    return tfidf_matrix, tfidf, word_freq

def machine_learning_models(tfidf_matrix, sentiment_labels):
    """机器学习预测模型"""
    print("4. 机器学习预测模型...")
    
    X = tfidf_matrix
    y = sentiment_labels
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    # 三种模型
    models = {
        '决策树': DecisionTreeClassifier(max_depth=5, criterion='entropy', random_state=42),
        '朴素贝叶斯': MultinomialNB(alpha=1.0),
        'SVM': SVC(kernel='linear', C=1, random_state=42)
    }
    
    results = {}
    for name, model in models.items():
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        f1 = f1_score(y_test, y_pred, average='weighted')
        results[name] = {'accuracy': accuracy, 'f1_score': f1, 'predictions': y_pred}
        print(f"{name} - 准确率: {accuracy:.3f}, F1分数: {f1:.3f}")
    
    # 混淆矩阵可视化
    fig, axes = plt.subplots(1, 3, figsize=(18, 5))
    for i, (name, result) in enumerate(results.items()):
        cm = confusion_matrix(y_test, result['predictions'])
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=['消极', '中性', '积极'], 
                   yticklabels=['消极', '中性', '积极'], ax=axes[i])
        axes[i].set_title(f'{name}混淆矩阵')
    
    plt.tight_layout()
    plt.savefig('机器学习模型评估.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return results

def clustering_and_topic_modeling(tfidf_matrix, segmented_comments):
    """聚类与主题建模"""
    print("5. K-means聚类与LDA主题建模...")
    
    # K-means聚类
    n_clusters = 3
    kmeans = KMeans(n_clusters=n_clusters, random_state=42)
    cluster_labels = kmeans.fit_predict(tfidf_matrix)
    
    # PCA降维可视化
    pca = PCA(n_components=2, random_state=42)
    tfidf_2d = pca.fit_transform(tfidf_matrix.toarray())
    
    plt.figure(figsize=(10, 8))
    colors = ['red', 'blue', 'green']
    for i in range(n_clusters):
        mask = cluster_labels == i
        plt.scatter(tfidf_2d[mask, 0], tfidf_2d[mask, 1], 
                   c=colors[i], label=f'簇 {i}', alpha=0.6)
    
    plt.xlabel('第一主成分')
    plt.ylabel('第二主成分')
    plt.title('K-means聚类结果 (PCA降维)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('聚类分析结果.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"各簇样本数量: {pd.Series(cluster_labels).value_counts().sort_index()}")
    
    # LDA主题建模
    texts = segmented_comments
    dictionary = corpora.Dictionary(texts)
    corpus = [dictionary.doc2bow(text) for text in texts]
    
    num_topics = 4
    lda_model = models.LdaModel(
        corpus=corpus, id2word=dictionary, num_topics=num_topics, 
        random_state=42, passes=10, alpha='auto'
    )
    
    print(f"\nLDA主题分析结果:")
    for topic_id in range(num_topics):
        topic_words = lda_model.show_topic(topic_id, topn=10)
        print(f"主题 {topic_id}: {', '.join([word for word, prob in topic_words])}")
    
    # 保存LDA结果
    try:
        import pyLDAvis.gensim_models as gensimvis
        import pyLDAvis
        vis = gensimvis.prepare(lda_model, corpus, dictionary)
        pyLDAvis.save_html(vis, '龙门石窟LDA主题分析.html')
        print('LDA可视化已保存为HTML文件')
    except:
        print('pyLDAvis未安装，跳过可视化')
    
    return cluster_labels, num_topics

def opinion_mining_and_network_analysis(cleaned_comments, segmented_comments, word_freq):
    """观点挖掘与社交网络分析"""
    print("6. 观点挖掘与社交网络分析...")

    # 观点提取
    def extract_opinions(text):
        opinions = []
        words = list(pseg.cut(text))
        for i in range(len(words) - 1):
            current_word, current_flag = words[i]
            next_word, next_flag = words[i + 1]
            if (current_flag in ['n', 'nr', 'ns'] and next_flag in ['a', 'ad'] and
                len(current_word) >= 2 and len(next_word) >= 2):
                opinions.append(f"{current_word} → {next_word}")
        return opinions

    all_opinions = []
    for comment in cleaned_comments:
        all_opinions.extend(extract_opinions(comment))

    opinion_freq = Counter(all_opinions)
    top_opinions = opinion_freq.most_common(15)
    print("Top15观点:")
    for opinion, count in top_opinions:
        print(f"{opinion}: {count}")

    # 社交网络构建
    user_keywords = defaultdict(set)
    for i, words in enumerate(segmented_comments):
        user_id = f"用户_{i}"
        high_freq_words = [word for word in words if word_freq.get(word, 0) >= 5]
        user_keywords[user_id].update(high_freq_words)

    # 构建网络图
    G = nx.Graph()
    threshold = 2
    user_list = list(user_keywords.keys())[:100]  # 限制用户数量

    for user in user_list:
        G.add_node(user)

    for i in range(len(user_list)):
        for j in range(i + 1, len(user_list)):
            user1, user2 = user_list[i], user_list[j]
            common_keywords = user_keywords[user1] & user_keywords[user2]
            if len(common_keywords) >= threshold:
                G.add_edge(user1, user2, weight=len(common_keywords))

    print(f"网络节点数: {G.number_of_nodes()}")
    print(f"网络边数: {G.number_of_edges()}")

    # 网络可视化
    if G.number_of_edges() > 0:
        plt.figure(figsize=(12, 10))
        pos = nx.spring_layout(G, k=1, iterations=50)
        nx.draw_networkx_nodes(G, pos, node_color='lightblue', node_size=50, alpha=0.7)
        nx.draw_networkx_edges(G, pos, alpha=0.5, width=0.5)
        plt.title('龙门石窟评论用户社交网络图')
        plt.axis('off')
        plt.savefig('龙门石窟社交网络图.png', dpi=300, bbox_inches='tight')
        plt.show()
    else:
        print('网络中没有边，建议降低阈值')

    return all_opinions, G

def generate_summary_report(comments, sentiment_scores, sentiment_labels, word_freq,
                          results, n_clusters, num_topics, all_opinions, G,
                          cleaned_comments, cluster_labels):
    """生成分析总结报告"""
    print("7. 生成分析总结报告...")

    print("=== 龙门石窟评论分析总结 ===")
    print(f"1. 数据概况: 共{len(comments)}条评论")
    print(f"2. 情感分析: 平均情感分数{np.mean(sentiment_scores):.3f}")
    print(f"3. 情感分布: {dict(pd.Series(sentiment_labels).value_counts())}")
    print(f"4. 高频词TOP5: {[word for word, _ in word_freq.most_common(5)]}")
    print(f"5. 机器学习: 最佳模型准确率{max([r['accuracy'] for r in results.values()]):.3f}")
    print(f"6. 聚类分析: 分为{n_clusters}个主题簇")
    print(f"7. 主题建模: 识别出{num_topics}个主要话题")
    print(f"8. 观点挖掘: 提取{len(all_opinions)}个观点表达")
    print(f"9. 社交网络: {G.number_of_nodes()}个节点，{G.number_of_edges()}条边")

    # 保存结果到Excel
    result_df = pd.DataFrame({
        '评论': cleaned_comments,
        '情感分数': sentiment_scores,
        '情感分类': sentiment_labels,
        '聚类标签': cluster_labels
    })
    result_df.to_excel('龙门石窟评论分析结果.xlsx', index=False)
    print('\n分析结果已保存到Excel文件')

def main():
    """主函数"""
    print("开始龙门石窟评论多模态文本分析...")

    # 1. 数据预处理
    comments, cleaned_comments, segmented_comments, users = load_and_preprocess_data()

    # 2. 情感分析
    sentiment_scores, sentiment_labels = sentiment_analysis(cleaned_comments)

    # 3. 词频分析
    tfidf_matrix, tfidf, word_freq = word_frequency_analysis(segmented_comments)

    # 4. 机器学习模型
    results = machine_learning_models(tfidf_matrix, sentiment_labels)

    # 5. 聚类与主题建模
    cluster_labels, num_topics = clustering_and_topic_modeling(tfidf_matrix, segmented_comments)

    # 6. 观点挖掘与网络分析
    all_opinions, G = opinion_mining_and_network_analysis(cleaned_comments, segmented_comments, word_freq)

    # 7. 生成总结报告
    generate_summary_report(comments, sentiment_scores, sentiment_labels, word_freq,
                          results, 3, num_topics, all_opinions, G,
                          cleaned_comments, cluster_labels)

    print("\n分析完成！所有结果已保存。")

if __name__ == "__main__":
    main()
