# 龙门石窟评论分析 - 运行说明

## 环境要求

### Python版本
- Python 3.7+

### 必需的Python包
```bash
pip install pandas numpy matplotlib seaborn jieba snownlp wordcloud scikit-learn gensim networkx openpyxl
```

### 可选包（用于高级可视化）
```bash
pip install pyLDAvis plotly
```

## 文件结构

```
项目目录/
├── 携程洛阳龙门石窟景点评论.xlsx    # 原始数据文件
├── 龙门石窟评论完整分析.ipynb       # Jupyter Notebook版本
├── 龙门石窟评论分析.py              # Python脚本版本
├── 龙门石窟评论分析报告.md          # 分析报告
├── 运行说明.md                      # 本文件
└── requirements.txt                 # 依赖包列表
```

## 运行方式

### 方式一：Jupyter Notebook（推荐）
1. 启动Jupyter Notebook
```bash
jupyter notebook
```

2. 打开 `龙门石窟评论完整分析.ipynb`

3. 按顺序执行所有单元格

### 方式二：Python脚本
```bash
python 龙门石窟评论分析.py
```

## 输出文件

运行完成后将生成以下文件：

### 数据文件
- `龙门石窟评论分析结果.xlsx` - 包含情感分数、分类、聚类标签的完整结果

### 可视化图片
- `情感分析结果.png` - 情感分布直方图和饼图
- `龙门石窟词云图.png` - 评论词云图
- `机器学习模型评估.png` - 三种模型的混淆矩阵
- `聚类分析结果.png` - K-means聚类PCA可视化
- `龙门石窟社交网络图.png` - 用户社交网络图

### 交互式文件
- `龙门石窟LDA主题分析.html` - LDA主题模型交互式可视化（需要pyLDAvis）

## 分析模块说明

### 1. 数据预处理
- 文本清洗和中文分词
- 自定义词典加载
- 停用词过滤

### 2. 情感分析
- 使用SnowNLP计算情感分数
- 情感分类（积极/中性/消极）
- 情感分布可视化

### 3. 词频分析
- TF-IDF特征提取
- 高频词统计
- 词云图生成

### 4. 机器学习
- 决策树分类器
- 朴素贝叶斯分类器
- 支持向量机（SVM）
- 模型性能评估和可视化

### 5. 聚类分析
- K-means聚类（3个簇）
- PCA降维可视化
- 聚类特征分析

### 6. 主题建模
- LDA主题模型（4个主题）
- 主题关键词提取
- 交互式可视化

### 7. 观点挖掘
- 基于词性标注的观点提取
- 观点频次统计

### 8. 社交网络分析
- 用户关键词相似性计算
- 网络图构建和可视化
- 网络特征分析

## 参数调整

### 情感分析阈值
```python
# 在代码中修改以下阈值
sentiment_labels = ['积极' if s > 0.6 else '消极' if s < 0.4 else '中性' for s in sentiment_scores]
```

### 聚类数量
```python
n_clusters = 3  # 可调整为其他数值
```

### 主题数量
```python
num_topics = 4  # 可调整为其他数值
```

### 社交网络阈值
```python
threshold = 2  # 共同关键词阈值
```

## 常见问题

### Q1: 运行时出现编码错误
**解决方案**：确保Excel文件编码为UTF-8，或在代码中指定编码：
```python
df = pd.read_excel('携程洛阳龙门石窟景点评论.xlsx', encoding='utf-8')
```

### Q2: 词云图显示乱码
**解决方案**：
1. 下载中文字体文件（如simhei.ttf）
2. 在WordCloud中指定字体路径：
```python
wordcloud = WordCloud(font_path='simhei.ttf', ...)
```

### Q3: pyLDAvis安装失败
**解决方案**：
- 使用conda安装：`conda install -c conda-forge pyldavis`
- 或跳过LDA可视化部分

### Q4: 内存不足
**解决方案**：
- 减少TF-IDF特征数量：`max_features=1000`
- 限制处理的评论数量
- 降低聚类和网络分析的数据规模

## 性能优化建议

1. **大数据集处理**：
   - 使用数据采样减少计算量
   - 分批处理避免内存溢出

2. **加速分词**：
   - 预先加载jieba词典
   - 使用并行处理

3. **可视化优化**：
   - 降低图片分辨率节省存储
   - 使用交互式图表提升体验

## 扩展功能

### 1. 时间序列分析
添加评论时间维度，分析情感变化趋势

### 2. 地域分析
基于IP地址分析不同地区用户的评价差异

### 3. 对比分析
与其他景点评论进行横向对比

### 4. 实时监控
构建自动化分析流水线，定期更新分析结果

## 技术支持

如遇到技术问题，请检查：
1. Python版本兼容性
2. 依赖包版本
3. 数据文件格式
4. 系统内存和存储空间

---

**注意事项**：
- 首次运行可能需要下载jieba词典，请保持网络连接
- 某些可视化功能需要GUI支持，在服务器环境中可能无法显示
- 建议在运行前备份原始数据文件
