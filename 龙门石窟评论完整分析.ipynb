{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 携程洛阳龙门石窟景点评论多模态文本分析\n", "## 完整分析流程"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入所有必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import jieba\n", "import jieba.posseg as pseg\n", "from snownlp import SnowNLP\n", "from wordcloud import WordCloud\n", "import re\n", "from collections import Counter, defaultdict\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 机器学习\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.svm import SVC\n", "from sklearn.metrics import accuracy_score, f1_score, confusion_matrix, classification_report\n", "from sklearn.cluster import KMeans\n", "from sklearn.decomposition import PCA\n", "\n", "# 主题建模和网络分析\n", "from gensim import corpora, models\n", "import networkx as nx\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']\n", "plt.rcParams['axes.unicode_minus'] = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1. 数据加载与预处理\n", "df = pd.read_excel('携程洛阳龙门石窟景点评论.xlsx')\n", "print(f\"数据形状: {df.shape}\")\n", "\n", "# 提取关键字段\n", "comments = df['字段3'].astype(str).tolist()\n", "ratings = df['字段2'].astype(str).tolist()\n", "users = df['字段1'].astype(str).tolist()\n", "\n", "# 自定义词典\n", "custom_words = ['龙门石窟', '石窟', '佛像', '卢舍那大佛', '奉先寺', '万佛洞', '古阳洞', '宾阳洞', '莲花洞', '药方洞', '伊河', '洛阳', '北魏', '唐代', '雕刻', '文化遗产', '世界遗产', '佛教', '艺术', '历史', '文化', '古迹', '景区', '门票', '导游', '讲解', '游览', '参观', '拍照', '摄影']\n", "for word in custom_words:\n", "    jieba.add_word(word)\n", "\n", "# 停用词\n", "stop_words = set(['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '里', '就是', '还是', '比较', '非常', '特别', '真的', '觉得', '感觉', '可以', '应该', '已经', '如果', '因为', '所以', '但是', '不过', '还有', '只是', '或者', '虽然', '然后', '而且'])\n", "\n", "# 文本清洗和分词\n", "def clean_and_segment(text):\n", "    text = re.sub(r'[^\\u4e00-\\u9fa5a-zA-Z0-9\\s]', '', text)\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    words = jieba.lcut(text, cut_all=False)\n", "    return [word for word in words if word not in stop_words and len(word) >= 2]\n", "\n", "cleaned_comments = [re.sub(r'[^\\u4e00-\\u9fa5a-zA-Z0-9\\s]', '', comment) for comment in comments]\n", "segmented_comments = [clean_and_segment(comment) for comment in cleaned_comments]\n", "\n", "print(\"数据预处理完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2. 情感分析\n", "sentiment_scores = [SnowNLP(comment).sentiments for comment in cleaned_comments]\n", "sentiment_labels = ['积极' if s > 0.6 else '消极' if s < 0.4 else '中性' for s in sentiment_scores]\n", "\n", "# 情感分布可视化\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "ax1.hist(sentiment_scores, bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "ax1.set_xlabel('情感分数')\n", "ax1.set_ylabel('频次')\n", "ax1.set_title('情感分数分布直方图')\n", "ax1.axvline(np.mean(sentiment_scores), color='red', linestyle='--', label=f'平均值: {np.mean(sentiment_scores):.3f}')\n", "ax1.legend()\n", "\n", "sentiment_counts = pd.Series(sentiment_labels).value_counts()\n", "ax2.pie(sentiment_counts.values, labels=sentiment_counts.index, autopct='%1.1f%%', colors=['#ff9999', '#66b3ff', '#99ff99'], startangle=90)\n", "ax2.set_title('情感倾向分布饼图')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"平均情感分数: {np.mean(sentiment_scores):.3f}\")\n", "print(f\"情感分布: {sentiment_counts}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 3. 词频分析与词云\n", "# TF-IDF分析\n", "text_for_tfidf = [' '.join(words) for words in segmented_comments]\n", "tfidf = TfidfVectorizer(max_features=2000, ngram_range=(1, 2))\n", "tfidf_matrix = tfidf.fit_transform(text_for_tfidf)\n", "\n", "feature_names = tfidf.get_feature_names_out()\n", "tfidf_scores = tfidf_matrix.sum(axis=0).A1\n", "word_freq_df = pd.DataFrame({'词语': feature_names, 'TF-IDF分数': tfidf_scores}).sort_values('TF-IDF分数', ascending=False)\n", "\n", "print(\"TF-IDF权重最高的20个词:\")\n", "print(word_freq_df.head(20))\n", "\n", "# 词频统计\n", "all_words = [word for words in segmented_comments for word in words]\n", "word_freq = Counter(all_words)\n", "\n", "# 生成词云\n", "wordcloud = WordCloud(width=800, height=600, background_color='white', max_words=100, colormap='viridis').generate_from_frequencies(word_freq)\n", "\n", "plt.figure(figsize=(12, 8))\n", "plt.imshow(wordcloud, interpolation='bilinear')\n", "plt.axis('off')\n", "plt.title('龙门石窟评论词云图', fontsize=16, pad=20)\n", "plt.show()\n", "\n", "wordcloud.to_file('龙门石窟词云图.png')\n", "print(\"词云图已保存\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 4. 机器学习预测模型\n", "X = tfidf_matrix\n", "y = sentiment_labels\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42, stratify=y)\n", "\n", "# 三种模型\n", "models = {\n", "    '决策树': DecisionTreeClassifier(max_depth=5, criterion='entropy', random_state=42),\n", "    '朴素贝叶斯': MultinomialNB(alpha=1.0),\n", "    'SVM': SVC(kernel='linear', C=1, random_state=42)\n", "}\n", "\n", "results = {}\n", "for name, model in models.items():\n", "    model.fit(X_train, y_train)\n", "    y_pred = model.predict(X_test)\n", "    accuracy = accuracy_score(y_test, y_pred)\n", "    f1 = f1_score(y_test, y_pred, average='weighted')\n", "    results[name] = {'accuracy': accuracy, 'f1_score': f1, 'predictions': y_pred}\n", "    print(f\"{name} - 准确率: {accuracy:.3f}, F1分数: {f1:.3f}\")\n", "\n", "# 混淆矩阵可视化\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "for i, (name, result) in enumerate(results.items()):\n", "    cm = confusion_matrix(y_test, result['predictions'])\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=['消极', '中性', '积极'], yticklabels=['消极', '中性', '积极'], ax=axes[i])\n", "    axes[i].set_title(f'{name}混淆矩阵')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 5. K-means聚类与LDA主题建模\n", "# K-means聚类\n", "n_clusters = 3\n", "kmeans = KMeans(n_clusters=n_clusters, random_state=42)\n", "cluster_labels = kmeans.fit_predict(tfidf_matrix)\n", "\n", "# PCA降维可视化\n", "pca = PCA(n_components=2, random_state=42)\n", "tfidf_2d = pca.fit_transform(tfidf_matrix.toarray())\n", "\n", "plt.figure(figsize=(10, 8))\n", "colors = ['red', 'blue', 'green']\n", "for i in range(n_clusters):\n", "    mask = cluster_labels == i\n", "    plt.scatter(tfidf_2d[mask, 0], tfidf_2d[mask, 1], c=colors[i], label=f'簇 {i}', alpha=0.6)\n", "plt.xlabel('第一主成分')\n", "plt.ylabel('第二主成分')\n", "plt.title('K-means聚类结果 (PCA降维)')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.show()\n", "\n", "print(f\"各簇样本数量: {pd.Series(cluster_labels).value_counts().sort_index()}\")\n", "\n", "# LDA主题建模\n", "texts = segmented_comments\n", "dictionary = corpora.Dictionary(texts)\n", "corpus = [dictionary.doc2bow(text) for text in texts]\n", "\n", "num_topics = 4\n", "lda_model = models.LdaModel(corpus=corpus, id2word=dictionary, num_topics=num_topics, random_state=42, passes=10, alpha='auto')\n", "\n", "print(f\"\\nLDA主题分析结果:\")\n", "for topic_id in range(num_topics):\n", "    topic_words = lda_model.show_topic(topic_id, topn=10)\n", "    print(f\"主题 {topic_id}: {', '.join([word for word, prob in topic_words])}\")\n", "\n", "# 保存LDA结果\n", "try:\n", "    import pyLDAvis.gensim_models as gensimvis\n", "    import pyLDAvis\n", "    vis = gensimvis.prepare(lda_model, corpus, dictionary)\n", "    pyLDAvis.save_html(vis, '龙门石窟LDA主题分析.html')\n", "    print('LDA可视化已保存为HTML文件')\n", "except:\n", "    print('pyLDAvis未安装，跳过可视化')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 6. 观点挖掘与社交网络分析\n", "# 观点提取\n", "def extract_opinions(text):\n", "    opinions = []\n", "    words = list(pseg.cut(text))\n", "    for i in range(len(words) - 1):\n", "        current_word, current_flag = words[i]\n", "        next_word, next_flag = words[i + 1]\n", "        if current_flag in ['n', 'nr', 'ns'] and next_flag in ['a', 'ad'] and len(current_word) >= 2 and len(next_word) >= 2:\n", "            opinions.append(f\"{current_word} → {next_word}\")\n", "    return opinions\n", "\n", "all_opinions = []\n", "for comment in cleaned_comments:\n", "    all_opinions.extend(extract_opinions(comment))\n", "\n", "opinion_freq = Counter(all_opinions)\n", "top_opinions = opinion_freq.most_common(15)\n", "print(\"Top15观点:\")\n", "for opinion, count in top_opinions:\n", "    print(f\"{opinion}: {count}\")\n", "\n", "# 社交网络构建\n", "user_keywords = defaultdict(set)\n", "for i, words in enumerate(segmented_comments):\n", "    user_id = f\"用户_{i}\"\n", "    high_freq_words = [word for word in words if word_freq.get(word, 0) >= 5]\n", "    user_keywords[user_id].update(high_freq_words)\n", "\n", "# 构建网络图\n", "G = nx.Graph()\n", "threshold = 2\n", "user_list = list(user_keywords.keys())[:100]  # 限制用户数量\n", "\n", "for user in user_list:\n", "    G.add_node(user)\n", "\n", "for i in range(len(user_list)):\n", "    for j in range(i + 1, len(user_list)):\n", "        user1, user2 = user_list[i], user_list[j]\n", "        common_keywords = user_keywords[user1] & user_keywords[user2]\n", "        if len(common_keywords) >= threshold:\n", "            G.add_edge(user1, user2, weight=len(common_keywords))\n", "\n", "print(f\"网络节点数: {G.number_of_nodes()}\")\n", "print(f\"网络边数: {G.number_of_edges()}\")\n", "\n", "# 网络可视化\n", "if G.number_of_edges() > 0:\n", "    plt.figure(figsize=(12, 10))\n", "    pos = nx.spring_layout(G, k=1, iterations=50)\n", "    nx.draw_networkx_nodes(G, pos, node_color='lightblue', node_size=50, alpha=0.7)\n", "    nx.draw_networkx_edges(G, pos, alpha=0.5, width=0.5)\n", "    plt.title('龙门石窟评论用户社交网络图')\n", "    plt.axis('off')\n", "    plt.show()\n", "    plt.savefig('龙门石窟社交网络图.png', dpi=300, bbox_inches='tight')\n", "else:\n", "    print('网络中没有边，建议降低阈值')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 7. 分析总结\n", "print(\"=== 龙门石窟评论分析总结 ===\")\n", "print(f\"1. 数据概况: 共{len(comments)}条评论\")\n", "print(f\"2. 情感分析: 平均情感分数{np.mean(sentiment_scores):.3f}\")\n", "print(f\"3. 情感分布: {dict(pd.Series(sentiment_labels).value_counts())}\")\n", "print(f\"4. 高频词TOP5: {[word for word, count in word_freq.most_common(5)]}\")\n", "print(f\"5. 机器学习: 最佳模型准确率{max([r['accuracy'] for r in results.values()]):.3f}\")\n", "print(f\"6. 聚类分析: 分为{n_clusters}个主题簇\")\n", "print(f\"7. 主题建模: 识别出{num_topics}个主要话题\")\n", "print(f\"8. 观点挖掘: 提取{len(all_opinions)}个观点表达\")\n", "print(f\"9. 社交网络: {G.number_of_nodes()}个节点，{G.number_of_edges()}条边\")\n", "\n", "# 保存结果到Excel\n", "result_df = pd.DataFrame({\n", "    '评论': cleaned_comments,\n", "    '情感分数': sentiment_scores,\n", "    '情感分类': sentiment_labels,\n", "    '聚类标签': cluster_labels\n", "})\n", "result_df.to_excel('龙门石窟评论分析结果.xlsx', index=False)\n", "print('\\n分析结果已保存到Excel文件')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}