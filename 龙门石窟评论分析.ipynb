{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 携程洛阳龙门石窟景点评论多模态文本分析\n", "\n", "## 项目概述\n", "本项目对携程洛阳龙门石窟景点评论进行全面的文本分析，包括情感分析、词频统计、机器学习预测、主题建模和社交网络分析。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import jieba\n", "import jieba.posseg as pseg\n", "from snownlp import SnowNLP\n", "from wordcloud import WordCloud\n", "import re\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 机器学习相关\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.svm import SVC\n", "from sklearn.metrics import accuracy_score, f1_score, confusion_matrix, classification_report\n", "from sklearn.cluster import KMeans\n", "from sklearn.decomposition import PCA\n", "\n", "# 主题建模\n", "from gensim import corpora, models\n", "import pyLDAvis.gensim_models as gensimvis\n", "import pyLDAvis\n", "\n", "# 网络分析\n", "import networkx as nx\n", "from collections import Counter, defaultdict\n", "import itertools"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 数据加载与预处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 读取数据\n", "df = pd.read_excel('携程洛阳龙门石窟景点评论.xlsx')\n", "print(f\"数据形状: {df.shape}\")\n", "print(f\"列名: {df.columns.tolist()}\")\n", "print(\"\\n前5行数据:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 提取评论文本和评分\n", "comments = df['字段3'].astype(str).tolist()  # 评论内容\n", "ratings = df['字段2'].astype(str).tolist()   # 评分\n", "users = df['字段1'].astype(str).tolist()     # 用户名\n", "\n", "print(f\"评论总数: {len(comments)}\")\n", "print(f\"评分分布: {pd.Series(ratings).value_counts()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 自定义词典和停用词\n", "custom_words = [\n", "    '龙门石窟', '石窟', '佛像', '卢舍那大佛', '奉先寺', '万佛洞', '古阳洞',\n", "    '宾阳洞', '莲花洞', '药方洞', '伊河', '洛阳', '北魏', '唐代', '雕刻',\n", "    '文化遗产', '世界遗产', '佛教', '艺术', '历史', '文化', '古迹',\n", "    '景区', '门票', '导游', '讲解', '游览', '参观', '拍照', '摄影'\n", "]\n", "\n", "# 添加自定义词典\n", "for word in custom_words:\n", "    jieba.add_word(word)\n", "\n", "# 中文停用词\n", "stop_words = set([\n", "    '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '里', '就是', '还是', '比较', '非常', '特别', '真的', '觉得', '感觉', '可以', '应该', '已经', '如果', '因为', '所以', '但是', '不过', '还有', '只是', '或者', '虽然', '然后', '而且', '不仅', '不但', '无论', '不管', '即使', '尽管', '除了', '包括', '根据', '按照', '通过', '由于', '为了', '关于', '对于', '至于', '除非', '只要', '一旦', '一边', '一面', '一方面', '另一方面', '总之', '总的来说', '一般来说', '换句话说', '也就是说', '比如说', '举例来说', '具体来说', '简单来说', '坦率地说', '老实说', '说实话', '事实上', '实际上', '确实', '的确', '当然', '自然', '显然', '明显', '清楚', '明白', '了解', '知道', '认为', '以为', '觉得', '感到', '发现', '注意到', '意识到', 'realize', '明确', '肯定', '确定', '绝对', '完全', '十分', '相当', '比较', '更加', '最', '极', '太', '挺', '蛮', '还', '再', '又', '也', '都', '只', '就', '才', '便', '即', '则', '却', '但', '而', '或', '及', '以及', '并且', '而且', '不仅', '不但', '既', '又', '一边', '一面', '有时', '时而', '偶尔', '经常', '总是', '从来', '曾经', '已经', '正在', '将要', '快要', '马上', '立刻', '立即', '顿时', '突然', '忽然', '渐渐', '逐渐', '慢慢', '赶紧', '连忙', '急忙'\n", "])\n", "\n", "print(f\"自定义词典词数: {len(custom_words)}\")\n", "print(f\"停用词数量: {len(stop_words)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 文本清洗函数\n", "def clean_text(text):\n", "    \"\"\"清洗文本：去除标点、特殊符号、数字等\"\"\"\n", "    # 去除网址\n", "    text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)\n", "    # 去除邮箱\n", "    text = re.sub(r'\\S+@\\S+', '', text)\n", "    # 去除特殊符号和标点\n", "    text = re.sub(r'[^\\u4e00-\\u9fa5a-zA-Z0-9\\s]', '', text)\n", "    # 去除多余空格\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    return text\n", "\n", "def segment_text(text):\n", "    \"\"\"分词并去除停用词\"\"\"\n", "    words = jieba.lcut(text, cut_all=False)  # 精确模式\n", "    # 过滤停用词和长度小于2的词\n", "    words = [word for word in words if word not in stop_words and len(word) >= 2]\n", "    return words\n", "\n", "# 清洗所有评论文本\n", "cleaned_comments = [clean_text(comment) for comment in comments]\n", "segmented_comments = [segment_text(comment) for comment in cleaned_comments]\n", "\n", "print(\"文本预处理完成\")\n", "print(f\"原始评论示例: {comments[0][:100]}...\")\n", "print(f\"清洗后评论示例: {cleaned_comments[0][:100]}...\")\n", "print(f\"分词结果示例: {segmented_comments[0][:10]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 情感倾向分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 使用SnowNLP进行情感分析\n", "def get_sentiment_score(text):\n", "    \"\"\"获取情感评分 (0-1, 越接近1越积极)\"\"\"\n", "    try:\n", "        s = SnowNLP(text)\n", "        return s.sentiments\n", "    except:\n", "        return 0.5  # 中性\n", "\n", "# 计算所有评论的情感分数\n", "sentiment_scores = [get_sentiment_score(comment) for comment in cleaned_comments]\n", "\n", "# 情感分类\n", "def classify_sentiment(score):\n", "    if score > 0.6:\n", "        return '积极'\n", "    elif score < 0.4:\n", "        return '消极'\n", "    else:\n", "        return '中性'\n", "\n", "sentiment_labels = [classify_sentiment(score) for score in sentiment_scores]\n", "\n", "print(f\"情感分析完成，共分析 {len(sentiment_scores)} 条评论\")\n", "print(f\"平均情感分数: {np.mean(sentiment_scores):.3f}\")\n", "print(f\"情感分布: {pd.Series(sentiment_labels).value_counts()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 情感分布可视化\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# 情感分数直方图\n", "ax1.hist(sentiment_scores, bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "ax1.set_xlabel('情感分数')\n", "ax1.set_ylabel('频次')\n", "ax1.set_title('情感分数分布直方图')\n", "ax1.axvline(np.mean(sentiment_scores), color='red', linestyle='--', label=f'平均值: {np.mean(sentiment_scores):.3f}')\n", "ax1.legend()\n", "\n", "# 情感分类饼图\n", "sentiment_counts = pd.Series(sentiment_labels).value_counts()\n", "colors = ['#ff9999', '#66b3ff', '#99ff99']\n", "ax2.pie(sentiment_counts.values, labels=sentiment_counts.index, autopct='%1.1f%%', \n", "        colors=colors, startangle=90)\n", "ax2.set_title('情感倾向分布饼图')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 保存情感分析结果\n", "sentiment_df = pd.DataFrame({\n", "    '评论': cleaned_comments,\n", "    '情感分数': sentiment_scores,\n", "    '情感分类': sentiment_labels\n", "})\n", "print(\"\\n情感分析结果示例:\")\n", "sentiment_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 高频词与词云分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TF-IDF分析\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "# 将分词结果转换为字符串\n", "text_for_tfidf = [' '.join(words) for words in segmented_comments]\n", "\n", "# 计算TF-IDF\n", "tfidf = TfidfVectorizer(max_features=2000, ngram_range=(1, 2))\n", "tfidf_matrix = tfidf.fit_transform(text_for_tfidf)\n", "\n", "# 获取特征名称和TF-IDF分数\n", "feature_names = tfidf.get_feature_names_out()\n", "tfidf_scores = tfidf_matrix.sum(axis=0).A1\n", "\n", "# 创建词频DataFrame\n", "word_freq_df = pd.DataFrame({\n", "    '词语': feature_names,\n", "    'TF-IDF分数': tfidf_scores\n", "}).sort_values('TF-IDF分数', ascending=False)\n", "\n", "print(\"TF-IDF权重最高的50个词:\")\n", "top_50_words = word_freq_df.head(50)\n", "print(top_50_words)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 词云生成\n", "from PIL import Image\n", "import numpy as np\n", "\n", "# 合并所有分词结果\n", "all_words = []\n", "for words in segmented_comments:\n", "    all_words.extend(words)\n", "\n", "# 统计词频\n", "word_freq = Counter(all_words)\n", "print(f\"总词数: {len(all_words)}\")\n", "print(f\"不重复词数: {len(word_freq)}\")\n", "\n", "# 高频词统计\n", "top_words = word_freq.most_common(30)\n", "print(\"\\n高频词TOP30:\")\n", "for word, count in top_words:\n", "    print(f\"{word}: {count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成词云图\n", "# 创建书本形状的mask（简化版）\n", "def create_book_mask():\n", "    mask = np.zeros((400, 600))\n", "    # 创建书本轮廓\n", "    mask[50:350, 100:500] = 255\n", "    return mask\n", "\n", "book_mask = create_book_mask()\n", "\n", "# 生成词云\n", "wordcloud = WordCloud(\n", "    # font_path='simhei.ttf',  # 如果没有字体文件，可以注释这行\n", "    width=800,\n", "    height=600,\n", "    background_color='white',\n", "    mask=book_mask,\n", "    max_words=100,\n", "    colormap='viridis'\n", ").generate_from_frequencies(word_freq)\n", "\n", "# 显示词云\n", "plt.figure(figsize=(12, 8))\n", "plt.imshow(wordcloud, interpolation='bilinear')\n", "plt.axis('off')\n", "plt.title('龙门石窟评论词云图', fontsize=16, pad=20)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 保存词云图\n", "wordcloud.to_file('龙门石窟词云图.png')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 机器学习预测模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 准备机器学习数据\n", "# 使用情感分类作为目标变量\n", "X = tfidf_matrix\n", "y = sentiment_labels\n", "\n", "# 划分训练集和测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42, stratify=y)\n", "\n", "print(f\"训练集大小: {X_train.shape[0]}\")\n", "print(f\"测试集大小: {X_test.shape[0]}\")\n", "print(f\"特征维度: {X_train.shape[1]}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 模型训练和评估\n", "models = {\n", "    '决策树': DecisionTreeClassifier(max_depth=5, criterion='entropy', random_state=42),\n", "    '朴素贝叶斯': MultinomialNB(alpha=1.0),\n", "    'SVM': SVC(kernel='linear', C=1, random_state=42)\n", "}\n", "\n", "results = {}\n", "\n", "for name, model in models.items():\n", "    print(f\"\\n训练 {name} 模型...\")\n", "    \n", "    # 训练模型\n", "    model.fit(X_train, y_train)\n", "    \n", "    # 预测\n", "    y_pred = model.predict(X_test)\n", "    \n", "    # 评估\n", "    accuracy = accuracy_score(y_test, y_pred)\n", "    f1 = f1_score(y_test, y_pred, average='weighted')\n", "    \n", "    results[name] = {\n", "        'accuracy': accuracy,\n", "        'f1_score': f1,\n", "        'predictions': y_pred\n", "    }\n", "    \n", "    print(f\"{name} - 准确率: {accuracy:.3f}, F1分数: {f1:.3f}\")\n", "    print(f\"分类报告:\\n{classification_report(y_test, y_pred)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 混淆矩阵可视化\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "\n", "for i, (name, result) in enumerate(results.items()):\n", "    cm = confusion_matrix(y_test, result['predictions'])\n", "    \n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "                xticklabels=['消极', '中性', '积极'], \n", "                yticklabels=['消极', '中性', '积极'],\n", "                ax=axes[i])\n", "    \n", "    axes[i].set_title(f'{name}混淆矩阵')\n", "    axes[i].set_xlabel('预测标签')\n", "    axes[i].set_ylabel('真实标签')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 模型性能对比\n", "performance_df = pd.DataFrame({\n", "    '模型': list(results.keys()),\n", "    '准确率': [results[name]['accuracy'] for name in results.keys()],\n", "    'F1分数': [results[name]['f1_score'] for name in results.keys()]\n", "})\n", "\n", "print(\"\\n模型性能对比:\")\n", "print(performance_df)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}